import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import ErrorBoundary from "@/components/ErrorBoundary";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Seedream AI Image Generator - Create Stunning Images with AI",
  description: "Transform your text descriptions into stunning visual artworks with Seedream AI. Free online AI image generator with multiple models and styles.",
  keywords: "AI image generator, text to image, artificial intelligence, digital art, creative tools, Seedream AI",
  authors: [{ name: "Seedream AI" }],
  creator: "Seedream AI",
  publisher: "Seedream AI",
  openGraph: {
    title: "Seedream AI Image Generator",
    description: "Transform your text descriptions into stunning visual artworks with AI",
    url: "https://seedream.pro",
    siteName: "Seedream AI",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Seedream AI Image Generator",
      },
    ],
    locale: "zh_CN",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Seedream AI Image Generator",
    description: "Transform your text descriptions into stunning visual artworks with AI",
    images: ["/og-image.jpg"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ErrorBoundary>
          {children}
        </ErrorBoundary>
      </body>
    </html>
  );
}
