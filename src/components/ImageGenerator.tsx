'use client';

import { useState } from 'react';

export default function ImageGenerator() {
  const [prompt, setPrompt] = useState('');
  const [selectedModel, setSelectedModel] = useState('flux-schnell');
  const [aspectRatio, setAspectRatio] = useState('1:1');
  const [isGenerating, setIsGenerating] = useState(false);

  const models = [
    { id: 'flux-schnell', name: 'Flux Schnell', credits: 1 },
    { id: 'flux-dev', name: 'Flux Dev', credits: 2 },
    { id: 'dall-e-3', name: 'DALL-E 3', credits: 3 },
    { id: 'midjourney', name: 'Midjourney', credits: 4 },
    { id: 'ideogram', name: 'Ideogram', credits: 2 },
    { id: 'kling', name: 'Kling 1.6', credits: 3 },
  ];

  const aspectRatios = [
    { id: '1:1', name: 'Square (1:1)', dimensions: '1024×1024' },
    { id: '16:9', name: 'Landscape (16:9)', dimensions: '1344×768' },
    { id: '9:16', name: 'Portrait (9:16)', dimensions: '768×1344' },
    { id: '4:3', name: 'Standard (4:3)', dimensions: '1152×896' },
    { id: '3:4', name: 'Portrait (3:4)', dimensions: '896×1152' },
  ];

  const handleGenerate = () => {
    if (!prompt.trim()) return;
    
    setIsGenerating(true);
    // Simulate generation process
    setTimeout(() => {
      setIsGenerating(false);
      alert('This is a frontend-only demo. In a real application, this would generate an image!');
    }, 3000);
  };

  return (
    <section id="image-generator" className="py-20 bg-white">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4 tracking-tight">
            Start Your Creative Journey
          </h2>
          <p className="text-xl text-slate-600 leading-relaxed">
            Enter your creative description, select models and parameters, and generate unique images
          </p>
        </div>

        <div className="bg-slate-50/50 rounded-2xl p-8 shadow-xl border border-slate-100">
          {/* Prompt Input */}
          <div className="mb-8">
            <label htmlFor="prompt" className="block text-sm font-semibold text-slate-700 mb-3">
              Describe your image
            </label>
            <textarea
              id="prompt"
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="A futuristic cityscape with neon lights and flying cars..."
              className="w-full px-4 py-4 border border-slate-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 resize-none transition-all duration-200 bg-white shadow-sm text-slate-900 placeholder-slate-400"
              rows={4}
            />
          </div>

          {/* Model Selection */}
          <div className="mb-8">
            <label className="block text-sm font-semibold text-slate-700 mb-4">
              Choose AI Model
            </label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {models.map((model) => (
                <button
                  key={model.id}
                  onClick={() => setSelectedModel(model.id)}
                  className={`p-4 rounded-xl border-2 transition-all duration-200 ${
                    selectedModel === model.id
                      ? 'border-purple-500 bg-purple-50 text-purple-700 shadow-lg'
                      : 'border-slate-200 bg-white text-slate-700 hover:border-slate-300 hover:shadow-md'
                  }`}
                >
                  <div className="font-semibold">{model.name}</div>
                  <div className="text-sm text-slate-500">{model.credits} Credits</div>
                </button>
              ))}
            </div>
          </div>

          {/* Aspect Ratio Selection */}
          <div className="mb-8">
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Aspect Ratio
            </label>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
              {aspectRatios.map((ratio) => (
                <button
                  key={ratio.id}
                  onClick={() => setAspectRatio(ratio.id)}
                  className={`p-3 rounded-lg border-2 transition-all text-center ${
                    aspectRatio === ratio.id
                      ? 'border-purple-500 bg-purple-50 text-purple-700'
                      : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <div className="font-medium text-sm">{ratio.name}</div>
                  <div className="text-xs text-gray-500">{ratio.dimensions}</div>
                </button>
              ))}
            </div>
          </div>

          {/* Generate Button */}
          <div className="text-center">
            <button
              onClick={handleGenerate}
              disabled={!prompt.trim() || isGenerating}
              className={`px-8 py-4 rounded-full font-semibold text-lg transition-all ${
                !prompt.trim() || isGenerating
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700 shadow-lg hover:shadow-xl transform hover:-translate-y-1'
              }`}
            >
              {isGenerating ? (
                <div className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Generating...
                </div>
              ) : (
                'Generate Image'
              )}
            </button>
          </div>

          {/* Info */}
          <div className="mt-6 text-center text-sm text-gray-500">
            <p>This is a frontend demo. No actual image generation occurs.</p>
          </div>
        </div>
      </div>
    </section>
  );
}
