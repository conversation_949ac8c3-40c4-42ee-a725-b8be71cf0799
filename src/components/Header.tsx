'use client';

import { useState } from 'react';
import Image from 'next/image';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <header className="bg-black/95 backdrop-blur-sm border-b border-gray-800 sticky top-0 z-50 shadow-lg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <div className="flex-shrink-0 flex items-center">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-600 via-purple-700 to-blue-600 rounded-lg flex items-center justify-center mr-3 shadow-lg">
                <span className="text-white font-bold text-sm">S</span>
              </div>
              <span className="text-xl font-bold text-white tracking-tight">Seedream AI Image Generator</span>
            </div>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <a href="#gallery" className="text-gray-300 hover:text-white transition-colors duration-200 font-medium">
              Gallery
            </a>
            <a href="#styles" className="text-gray-300 hover:text-white transition-colors duration-200 font-medium">
              Style
            </a>
            <a href="#pricing" className="text-gray-300 hover:text-white transition-colors duration-200 font-medium">
              Pricing
            </a>
          </nav>

          {/* Right side */}
          <div className="hidden md:flex items-center space-x-6">
            {/* Language Selector */}
            <select className="text-sm text-gray-300 bg-transparent border-none focus:outline-none cursor-pointer hover:text-white transition-colors duration-200">
              <option className="bg-black text-white">English</option>
              <option className="bg-black text-white">中文</option>
              <option className="bg-black text-white">日本語</option>
            </select>

            {/* CTA Button */}
            <a
              href="#image-generator"
              className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-6 py-2.5 rounded-full hover:from-purple-700 hover:to-blue-700 transition-all duration-200 font-semibold text-sm shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
            >
              Start Creating
            </a>

            {/* Sign In Button */}
            <button className="text-gray-300 hover:text-white transition-colors duration-200 font-medium">
              Sign In
            </button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-gray-600 hover:text-gray-900 focus:outline-none"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {isMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t border-gray-100">
              <a href="#gallery" className="block px-3 py-2 text-gray-600 hover:text-gray-900">
                Gallery
              </a>
              <a href="#styles" className="block px-3 py-2 text-gray-600 hover:text-gray-900">
                Style
              </a>
              <a href="#pricing" className="block px-3 py-2 text-gray-600 hover:text-gray-900">
                Pricing
              </a>
              <div className="px-3 py-2">
                <a
                  href="#image-generator"
                  className="block w-full text-center bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 py-2 rounded-full hover:from-purple-700 hover:to-blue-700 transition-all duration-200 font-medium"
                >
                  Start Creating
                </a>
              </div>
              <a href="#" className="block px-3 py-2 text-gray-600 hover:text-gray-900">
                Sign In
              </a>
            </div>
          </div>
        )}
      </div>
    </header>
  );
}
