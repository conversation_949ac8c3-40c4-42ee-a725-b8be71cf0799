'use client';

import { useState } from 'react';

export default function FAQ() {
  const [openIndex, setOpenIndex] = useState<number | null>(0);

  const faqs = [
    {
      question: 'What makes Seedream AI special?',
      answer: 'Seedream AI represents the next generation of AI image generation, built on groundbreaking technology with 12 billion parameters. Our hybrid architecture combines multimodal and parallel diffusion transformer blocks to create images that are not just beautiful, but truly understand your creative intent.'
    },
    {
      question: 'How does Seedream ensure quality?',
      answer: 'Our advanced AI model has been meticulously trained on diverse datasets, enabling it to understand context, style, and artistic nuances. This results in consistently high-quality images that capture your vision with remarkable accuracy.'
    },
    {
      question: 'Do I need any technical expertise?',
      answer: 'Not at all! Seedream AI is designed for everyone. Whether you\'re a professional artist or just starting your creative journey, our intuitive interface makes it easy to bring your ideas to life through simple text descriptions.'
    },
    {
      question: 'What types of images can I create?',
      answer: 'The possibilities are endless! From photorealistic scenes to artistic illustrations, abstract concepts to detailed character designs - if you can describe it, Seedream AI can create it.'
    },
    {
      question: 'How fast is the generation process?',
      answer: 'Thanks to our optimized architecture with rotary positional embeddings and parallel attention layers, most images are generated within seconds, allowing you to iterate and experiment quickly.'
    },
    {
      question: 'Are there any usage limits?',
      answer: 'No limits! We believe in making AI art accessible to everyone. Generate as many images as you want, completely free, with no registration required.'
    },
    {
      question: 'How can I get the best results?',
      answer: 'The key to great results is in your prompts. Be specific about what you want to see - include details about style, mood, lighting, and composition. Experiment with different descriptions to find what works best for your vision.'
    },
    {
      question: 'Can I use the images commercially?',
      answer: 'Yes, you have the freedom to use generated images for both personal and commercial purposes. We recommend reviewing our terms of service for specific usage guidelines.'
    }
  ];

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            FAQ
          </h2>
          <p className="text-xl text-gray-600">
            Have another question? Contact us on Discord or by email.
          </p>
        </div>

        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <div key={index} className="bg-white rounded-2xl shadow-lg overflow-hidden">
              <button
                onClick={() => toggleFAQ(index)}
                className="w-full px-6 py-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200"
              >
                <div className="flex items-center">
                  <span className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg flex items-center justify-center text-sm font-bold mr-4">
                    {index + 1}
                  </span>
                  <h3 className="text-lg font-semibold text-gray-900">
                    {faq.question}
                  </h3>
                </div>
                <svg
                  className={`w-6 h-6 text-gray-500 transform transition-transform duration-200 ${
                    openIndex === index ? 'rotate-180' : ''
                  }`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              
              <div
                className={`overflow-hidden transition-all duration-300 ease-in-out ${
                  openIndex === index ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                }`}
              >
                <div className="px-6 pb-6">
                  <div className="ml-12 text-gray-600 leading-relaxed">
                    {faq.answer}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-br from-purple-600 to-blue-600 rounded-2xl p-8 md:p-12 text-white">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              Start Creating Amazing AI Art
            </h3>
            <div className="w-16 h-1 bg-white/30 mx-auto mb-6"></div>
            <p className="text-xl mb-8 text-purple-100">
              Join thousands of artists and creators using Seedream AI Image Generator.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="#image-generator"
                className="bg-white text-purple-600 px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-200 font-semibold text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              >
                Start Creating
              </a>
              <a
                href="#gallery"
                className="bg-transparent text-white px-8 py-4 rounded-full border-2 border-white hover:bg-white hover:text-purple-600 transition-all duration-200 font-semibold text-lg"
              >
                Explore Gallery
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
