'use client';

import { useState } from 'react';

export default function FAQ() {
  const [openIndex, setOpenIndex] = useState<number | null>(0);

  const faqs = [
    {
      question: 'What AI models are available in Seedream AI Image Generator?',
      answer: 'Seedream AI Image Generator offers access to 12+ cutting-edge AI models including Stable Diffusion XL, DALL-E 3, Midjourney, Flux, Ideogram, Kling, and custom fine-tuned models. Each model is optimized for different artistic styles and use cases.'
    },
    {
      question: 'Can I use the generated images for commercial purposes?',
      answer: 'Yes! All images generated through Seedream AI Image Generator come with commercial usage rights. You can use them for business, marketing, products, and any commercial project without additional licensing fees.'
    },
    {
      question: 'What resolution can I generate images in?',
      answer: 'Seedream AI Image Generator supports image generation up to 4K resolution (4096x4096 pixels). You can choose from various aspect ratios and sizes to fit your specific needs.'
    },
    {
      question: 'How long does it take to generate an image?',
      answer: 'Most images are generated within 10-30 seconds depending on the model and complexity. Our optimized infrastructure ensures fast processing times even for high-resolution outputs.'
    },
    {
      question: 'Can I customize the artistic style of my images?',
      answer: 'Absolutely! Seedream AI Image Generator provides advanced prompt engineering tools, style presets, and artistic direction controls. You can specify artistic styles, color palettes, composition, and more.'
    },
    {
      question: 'Do you offer API access for developers?',
      answer: 'Yes! Seedream AI Image Generator provides a powerful API that allows developers to integrate AI image generation into their applications. The API supports all our models and features with comprehensive documentation.'
    },
    {
      question: 'What makes Seedream AI special?',
      answer: 'Seedream AI represents the next generation of AI image generation, built on groundbreaking technology with 12 billion parameters. Our hybrid architecture combines multimodal and parallel diffusion transformer blocks to create images that are not just beautiful, but truly understand your creative intent.'
    },
    {
      question: 'How can I get the best results?',
      answer: 'The key to great results is in your prompts. Be specific about what you want to see - include details about style, mood, lighting, and composition. Experiment with different descriptions to find what works best for your vision.'
    }
  ];

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section className="py-20 bg-slate-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4 tracking-tight">
            FAQ
          </h2>
          <p className="text-xl text-slate-600 leading-relaxed">
            Have another question? Contact us on Discord or by email.
          </p>
        </div>

        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <div key={index} className="bg-white rounded-2xl shadow-lg overflow-hidden">
              <button
                onClick={() => toggleFAQ(index)}
                className="w-full px-6 py-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200"
              >
                <div className="flex items-center">
                  <span className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg flex items-center justify-center text-sm font-bold mr-4">
                    {index + 1}
                  </span>
                  <h3 className="text-lg font-semibold text-gray-900">
                    {faq.question}
                  </h3>
                </div>
                <svg
                  className={`w-6 h-6 text-gray-500 transform transition-transform duration-200 ${
                    openIndex === index ? 'rotate-180' : ''
                  }`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              
              <div
                className={`overflow-hidden transition-all duration-300 ease-in-out ${
                  openIndex === index ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                }`}
              >
                <div className="px-6 pb-6">
                  <div className="ml-12 text-gray-600 leading-relaxed">
                    {faq.answer}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-br from-purple-600 to-blue-600 rounded-2xl p-8 md:p-12 text-white">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              Start Creating Amazing AI Art
            </h3>
            <div className="w-16 h-1 bg-white/30 mx-auto mb-6"></div>
            <p className="text-xl mb-8 text-purple-100">
              Join thousands of artists and creators using Seedream AI Image Generator.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="#image-generator"
                className="bg-white text-purple-600 px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-200 font-semibold text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              >
                Start Creating
              </a>
              <a
                href="#gallery"
                className="bg-transparent text-white px-8 py-4 rounded-full border-2 border-white hover:bg-white hover:text-purple-600 transition-all duration-200 font-semibold text-lg"
              >
                Explore Gallery
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
