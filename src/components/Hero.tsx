export default function Hero() {
  const aiModels = [
    { name: 'Midjourney', color: 'bg-purple-100 text-purple-800' },
    { name: 'Flux', color: 'bg-blue-100 text-blue-800' },
    { name: 'Ideogram', color: 'bg-green-100 text-green-800' },
    { name: 'DALL-E', color: 'bg-orange-100 text-orange-800' },
    { name: '<PERSON><PERSON>', color: 'bg-pink-100 text-pink-800' },
    { name: 'Replicate', color: 'bg-indigo-100 text-indigo-800' },
    { name: '<PERSON><PERSON>', color: 'bg-red-100 text-red-800' },
  ];

  return (
    <section className="relative bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 py-20 lg:py-32">
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-indigo-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          {/* Badge */}
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-white/80 backdrop-blur-sm border border-purple-200 text-purple-700 text-sm font-medium mb-8">
            <span className="mr-2">✨</span>
            New Era of AI Image Creation
          </div>

          {/* Main heading */}
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-gray-900 mb-6">
            <span className="block">Unleash Your Creativity with</span>
            <span className="block bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
              Seedream AI Image Generator
            </span>
          </h1>

          {/* Subtitle */}
          <p className="text-xl md:text-2xl text-gray-600 mb-12 max-w-3xl mx-auto">
            Transform your text descriptions into stunning visual artworks.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
            <a
              href="#image-generator"
              className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-8 py-4 rounded-full hover:from-purple-700 hover:to-blue-700 transition-all duration-200 font-semibold text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1"
            >
              Try for Free
            </a>
            <a
              href="#gallery"
              className="bg-white text-gray-700 px-8 py-4 rounded-full border border-gray-300 hover:bg-gray-50 transition-all duration-200 font-semibold text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1"
            >
              Explore Gallery
            </a>
          </div>

          {/* AI Models */}
          <div className="flex flex-wrap justify-center gap-3 mb-12">
            {aiModels.map((model, index) => (
              <span
                key={model.name}
                className={`px-4 py-2 rounded-full text-sm font-medium ${model.color} backdrop-blur-sm`}
              >
                {model.name}
              </span>
            ))}
          </div>

          {/* Call to action section */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-gray-200 shadow-xl max-w-4xl mx-auto">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Start Your Creative Journey
            </h2>
            <p className="text-gray-600 mb-6">
              Enter your creative description, select models and parameters, and generate unique images
            </p>
            
            {/* Quick example */}
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-purple-600">Flux Schnell</span>
                <span className="text-sm text-gray-500">1 Credits</span>
              </div>
              <div className="text-left text-gray-700">
                <div className="mb-2 p-2 bg-white rounded border text-sm">Cyberpunk Neon Cityscape</div>
                <div className="mb-2 p-2 bg-white rounded border text-sm">Fantasy Dragon Portrait</div>
                <div className="p-2 bg-white rounded border text-sm">Minimalist Product Photography</div>
              </div>
            </div>

            <a
              href="#image-generator"
              className="inline-block bg-gradient-to-r from-purple-600 to-blue-600 text-white px-6 py-3 rounded-full hover:from-purple-700 hover:to-blue-700 transition-all duration-200 font-medium"
            >
              Start Creating Now
            </a>
          </div>
        </div>
      </div>
    </section>
  );
}
